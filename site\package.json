{"name": "site", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/node": "^9.4.2", "@astrojs/react": "^4.3.0", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.12", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "astro": "^5.13.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.539.0", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.7"}}